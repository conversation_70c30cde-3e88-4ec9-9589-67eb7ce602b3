/**
 * 一口价功能测试文件
 * 用于验证新增的功能是否正常工作
 */

import { api } from '@/api-v2'

// 测试一口价列表API（包含原价字段）
export const testOnepriceListWithOriginalPrice = async () => {
  try {
    console.log('🧪 测试一口价列表API（包含原价字段）...')
    
    const params = {
      pageNum: 1,
      pageSize: 10
    }
    
    const result = await api.service.onepriceList(params)
    console.log('✅ 一口价列表API测试结果:', result)
    
    // 检查是否包含原价字段
    if (result.code === '200' && result.data && result.data.list) {
      const firstItem = result.data.list[0]
      if (firstItem) {
        console.log('📋 第一条数据:', firstItem)
        console.log('💰 原价字段:', firstItem.originalPrice)
        console.log('💰 价格字段:', firstItem.price)
        console.log('📝 配置项:', firstItem.configs)
      }
    }
    
    return result
  } catch (error) {
    console.error('❌ 一口价列表API测试失败:', error)
    throw error
  }
}

// 测试一口价配置项API
export const testOnepriceSettingList = async (serviceId = 517) => {
  try {
    console.log('🧪 测试一口价配置项API...', serviceId)
    
    const result = await api.service.onepriceSettingList({ serviceId })
    console.log('✅ 一口价配置项API测试结果:', result)
    
    if (result.code === '200' && result.data) {
      result.data.forEach((config, index) => {
        console.log(`📝 配置项 ${index + 1}:`)
        console.log(`   - ID: ${config.id}`)
        console.log(`   - 描述: ${config.problemDesc}`)
        console.log(`   - 类型: ${config.inputType} (${config.inputType === 3 ? '单选' : config.inputType === 4 ? '多选' : '其他'})`)
        console.log(`   - 选项: ${config.options}`)
        console.log(`   - 是否必填: ${config.isRequired === 1 ? '是' : '否'}`)
      })
    }
    
    return result
  } catch (error) {
    console.error('❌ 一口价配置项API测试失败:', error)
    throw error
  }
}

// 测试新增一口价（包含原价字段）
export const testOnepriceAddWithOriginalPrice = async () => {
  try {
    console.log('🧪 测试新增一口价API（包含原价字段）...')
    
    const data = {
      serviceId: 517,
      originalPrice: 87.00, // 新增原价字段
      price: 300,
      status: 1,
      priority: 12,
      num: 2,
      effectiveBegin: "2025-08-22 00:00:00",
      effectiveEnd: "2025-08-22 23:59:59",
      configs: [
        {
          settingId: 1147,
          values: ["多选1", "多选2"]
        },
        {
          settingId: 1156,
          values: ["单选1"]
        }
      ],
      remark: "测试新增一口价，包含原价字段"
    }
    
    console.log('📤 提交数据:', data)
    const result = await api.service.onepriceAdd(data)
    console.log('✅ 新增一口价API测试结果:', result)
    
    return result
  } catch (error) {
    console.error('❌ 新增一口价API测试失败:', error)
    throw error
  }
}

// 测试服务列表搜索
export const testServiceListSearch = async (searchQuery = '马桶') => {
  try {
    console.log('🧪 测试服务列表搜索API...', searchQuery)
    
    const result = await api.service.serviceList({ 
      title: searchQuery, 
      pageSize: 50 
    })
    console.log('✅ 服务列表搜索API测试结果:', result)
    
    if (result.code === '200' && result.data && result.data.list) {
      console.log(`🔍 搜索到 ${result.data.list.length} 个服务项目:`)
      result.data.list.forEach((service, index) => {
        console.log(`   ${index + 1}. ${service.title} (ID: ${service.id})`)
      })
    }
    
    return result
  } catch (error) {
    console.error('❌ 服务列表搜索API测试失败:', error)
    throw error
  }
}

// 运行所有测试
export const runAllTests = async () => {
  console.log('🚀 开始运行一口价功能测试...')
  
  try {
    // 测试1：一口价列表
    await testOnepriceListWithOriginalPrice()
    
    // 测试2：配置项列表
    await testOnepriceSettingList()
    
    // 测试3：服务搜索
    await testServiceListSearch()
    
    // 测试4：新增一口价
    // await testOnepriceAddWithOriginalPrice() // 注释掉，避免实际新增数据
    
    console.log('✅ 所有测试完成')
  } catch (error) {
    console.error('❌ 测试过程中出现错误:', error)
  }
}

// 在浏览器控制台中可以调用的测试函数
if (typeof window !== 'undefined') {
  window.testOnepriceFeatures = {
    testList: testOnepriceListWithOriginalPrice,
    testSettings: testOnepriceSettingList,
    testSearch: testServiceListSearch,
    testAdd: testOnepriceAddWithOriginalPrice,
    runAll: runAllTests
  }
  
  console.log('🧪 一口价功能测试已加载，可在控制台使用:')
  console.log('   - window.testOnepriceFeatures.testList() // 测试列表')
  console.log('   - window.testOnepriceFeatures.testSettings() // 测试配置项')
  console.log('   - window.testOnepriceFeatures.testSearch() // 测试搜索')
  console.log('   - window.testOnepriceFeatures.runAll() // 运行所有测试')
}

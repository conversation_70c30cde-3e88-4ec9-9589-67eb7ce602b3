/**
 * 一口价API测试文件
 * 用于验证API接口是否正确配置
 */

import { api } from '@/api-v2'

// 测试一口价列表API
export const testOnepriceList = async () => {
  try {
    console.log('🧪 测试一口价列表API...')
    
    const params = {
      pageNum: 1,
      pageSize: 10,
      serviceTitle: '马桶安装'
    }
    
    const result = await api.service.onepriceList(params)
    console.log('✅ 一口价列表API测试结果:', result)
    
    return result
  } catch (error) {
    console.error('❌ 一口价列表API测试失败:', error)
    throw error
  }
}

// 测试一口价详情API
export const testOnepriceInfo = async (id) => {
  try {
    console.log('🧪 测试一口价详情API...', id)
    
    const result = await api.service.onepriceInfo({ id })
    console.log('✅ 一口价详情API测试结果:', result)
    
    return result
  } catch (error) {
    console.error('❌ 一口价详情API测试失败:', error)
    throw error
  }
}

// 测试一口价新增API
export const testOnepriceAdd = async () => {
  try {
    console.log('🧪 测试一口价新增API...')
    
    const data = {
      serviceId: 517,
      price: 300,
      status: 1,
      priority: 12,
      num: 2,
      effectiveBegin: "2025-08-21 00:00:00",
      effectiveEnd: "2025-08-21 23:59:59",
      configs: [
        {
          settingId: 1147,
          values: ["多选12", "多选13"]
        },
        {
          settingId: 1156,
          values: ["单选1"]
        }
      ],
      remark: "水箱容量60L，高配，尺寸300-350mm"
    }
    
    const result = await api.service.onepriceAdd(data)
    console.log('✅ 一口价新增API测试结果:', result)
    
    return result
  } catch (error) {
    console.error('❌ 一口价新增API测试失败:', error)
    throw error
  }
}

// 测试一口价状态切换API
export const testOnepriceToggleStatus = async (id) => {
  try {
    console.log('🧪 测试一口价状态切换API...', id)
    
    const result = await api.service.onepriceToggleStatus({ id })
    console.log('✅ 一口价状态切换API测试结果:', result)
    
    return result
  } catch (error) {
    console.error('❌ 一口价状态切换API测试失败:', error)
    throw error
  }
}

// 测试一口价删除API
export const testOnepriceDelete = async (id) => {
  try {
    console.log('🧪 测试一口价删除API...', id)
    
    const result = await api.service.onepriceDelete({ id })
    console.log('✅ 一口价删除API测试结果:', result)
    
    return result
  } catch (error) {
    console.error('❌ 一口价删除API测试失败:', error)
    throw error
  }
}

// 运行所有测试
export const runAllTests = async () => {
  console.log('🚀 开始运行一口价API测试套件...')
  
  try {
    // 测试列表API
    await testOnepriceList()
    
    // 如果有数据，测试详情API
    const listResult = await testOnepriceList()
    if (listResult.code === '200' && listResult.data.list.length > 0) {
      const firstItem = listResult.data.list[0]
      await testOnepriceInfo(firstItem.id)
    }
    
    console.log('✅ 所有一口价API测试完成')
  } catch (error) {
    console.error('❌ 一口价API测试套件执行失败:', error)
  }
}
